package com.bello.lineage.common.constants;

/**
 * 静态变量的命名— 没有意义的，调整名字， 命名 规则 业务_类型_枚举名
 */
public class Constant {


    public static final String MESSAGE_SUCCESS = "操作成功";
    public static final String MESSAGE_FAIL = "操作失败";
    public static final String STATUS_DATA = "data";
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAIL = "error";
    public static final String RESULT_DATA_TOTALCOUNT = "total";
    public static final String RESULT_DATA_PAGE_SIZE = "pageSize";
    public static final String RESULT_DATA_PAGE_NUM = "pageNum";


    public static final String STATUS_ABLE = "1";
    public static final String STATUS_UNABLE = "0";

    public static final Integer USER_STATUS_ABLE = 1;
    public static final Integer USER_STATUS_UNABLE = 0;


    /************************************数字****************************************************/
    /**
     * 数值 -1
     */
    public static final int NUM_MINUS_1 = -1;

    /**
     * 数值 0
     */
    public static final int NUM_0 = 0;

    /**
     * 数值 1
     */
    public static final int NUM_1 = 1;


    /**
     * 数值 3
     */
    public static final int NUM_3 = 3;

    /**
     * 数值 5
     */
    public static final int NUM_5 = 5;

    /**
     * 数值 7
     */
    public static final int NUM_7 = 7;

    /**
     * 数值 8
     */
    public static final int NUM_8 = 8;

    /**
     * 数值 10
     */
    public static final int NUM_10 = 10;


    /***********************************通用符号************************************************/
    /**
     * 逗号(半角)：","
     */
    public static final String COMMA = ",";

    /**
     * 横线：-
     */
    public static final String HORIZONTAL_LINE = "-";


    /**
     * 空字符串：""
     */
    public static final String STRING_BLANK = "";

    public static final String API_PREFIX = "/api";

    public static final String UPLOAD_FILE_DELIMITER = "--";

    public static final String SEPARATOR = "/";

    public static final String RSP_SUCCESS = "0";

    public static final String DEFAULT_ORDER_TYPE = "desc";

    public static final String DEFAULT_ORDER_FIELD = "createTime";

    public static final Integer DEFAULT_PAGE_NUM = 1;

    public static final Integer DEFAULT_PAGE_SIZE = 10;

    public static final String TOKE_AUTH_4A_INPUT = "classpath:com/idss/ums/auth/sso/4a_auth_token_input.xml";

    public static final String ACCOUNT_SYNC_INPUT = "classpath:com/idss/ums/auth/sso/4a_auth_account_sync_input.xml";

    public static final String ACCOUNT_SYNC_OUTPUT = "classpath:com/idss/ums/auth/sso/4a_auth_account_sync_output.xml";

    public static final String REDIS_KEY = "portal:token:";

    /**
     *
     */
    public static final String STRING_0 = "0";


    /**
     * AUTH 认证参数默认值
     */

    public static final String AUTH_TOKEN = "12345";

    public static final String AUTH_APP_ACCT_ID = "sysadmin";


    /**
     * 4A有效账户标识
     */
    public static final String FOURA_STATUS_ACTIVE = "1";

    /**
     * 4A认证异常标识
     * 0:正常
     * 1:异常
     */
    public static final String FOURA_RESPONSE_FLAG_NORMAL = "0";
    public static final String FOURA_RESPONSE_FLAG = "1";

    /**
     * 用户账户状态
     * 0:正常
     * 1:锁定
     */
    public static final Integer USER_STATUS_NORMAL = 1;

    public static final Integer USER_STATUS_LOCK = 0;


    /**
     * 删除标识
     * 0:未删除
     * 1:已删除
     */
    public static final Integer DEL_FLAG_ABLE = 1;

    public static final Integer DEL_FLAG_UNABLE = 0;

    /**
     * 是否预置
     * 0：预置
     * 1:未预置
     */

    public static final Integer BUILTIN_ABLE = 1;

    public static final Integer BUILTIN_UNABLE = 0;


    /**
     * flag标识
     */

    public static final String FLAG_ABLE = "1";

    public static final String FLAG_UNABLE = "0";


    public static final String CONDITION_TYPE_1 = "1";

    public static final String CONDITION_TYPE_2 = "2";

    public static final String CONDITION_TYPE_3 = "3";

    public static final String CONDITION_TYPE_4 = "4";

    public static final String CONDITION_TYPE_5 = "5";

    public static final String CONDITION_TYPE_6 = "6";

    public static final String DATASOURCE_TYPE = "1";

    /*
      默认最大权限租户
     */
    public static final long TENANT_ID = 0;

    /*
     特殊模块id
     */
    public static final String MODULE_ID = "0";

    /*
    默认来源
    */
    public static final String SOURCE_TYPE = "01";

    /**
     * 默认value值
     */
    public static final String ROLE_FUNCTION_VALUE = "15";


    public static final int LOG_MAX_NUM = 600;
    public static final int LOG_CUT_MIN = 0;
    public static final int LOG_CUT_MAX = 597;

    /**
     * 默认value值
     */
    public static final String ONLINE = "online";
    public static final String ONLINE_NAME = "上线";
    public static final String OFFLINE_NAME = "下线";
    public static final String OFFLINE = "offline";
    public static final String FAIL = "失败";
    public static final String SUCCESS = "成功";


    //数据库连接默认信息

    public static final String DATASOURCE_TYPE_MYSQL = "1";
    public static final String DATASOURCE_TYPE_CLICKHOUSE = "2";
    public static final String DATASOURCE_TYPE_DEFAULT = "3";

    public static final String DATASOURCE_DRIVER_MYSQL = "com.mysql.cj.jdbc.Driver";
    public static final String DATASOURCE_DRIVER_CLICKHOUSE = "ru.yandex.clickhouse.ClickHouseDriver";
//    public static final String DATASOURCE_DRIVER_CLICKHOUSE = "com.clickhouse.jdbc.ClickHouseDriver";

    public static final String DATASOURCE_URL_MYSQL = "jdbc:mysql://";
    public static final String DATASOURCE_URL_CLICKHOUSE = "jdbc:clickhouse://";

    /**
     * 初始化SQL脚本存放路径
     */
    public static final String SCRIPTPATH = "tenant/";

    /**
     * SQL模板文件(clickhouse的sql脚本需要动态替换-如果mysql表引擎)
     */
    public static final String SCRIPTPATH_TEMPLATE = "template";

    /**
     * mysql脚本存放路径
     */
    public static final String SCRIPTPAHT_MYSQL = "mysql";

    /**
     * clickhouse脚本存放路径
     */
    public static final String SCRIPTPATH_CLICKHOUSE = "clickhouse";

    /**
     * 脚本文件类型-模板
     */
    public static final String SCRIPTTYPE_TEMPLATE = "template";

    /**
     * 脚本文件类型-sql脚本
     */
    public static final String SCRIPTTYPE_SQL = "sql";

    public static final String DB_MYSQL = "mysql";

    public static final String DB_CLICKHOUSE = "clickhouse";
    public static final String DATASOURCE_URL_HDFS = "hdfs://";

    public static final String JOB_ONCE = "01";
    public static final String JOB_CYCLE = "02";

    /**
     * redis租户系统日志key
     */
    public static final String TENANT_SYS_LOG_KEY = "sys_log:%s";


    //是否ssl
    public static final int STATUS_ENABLE = 1;
    public static final int STATUS_DISENABLE = 0;
}
