package com.bello.lineage.common.bean;

import com.bello.lineage.common.constants.Constant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.FieldError;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/3/25 16:38
 * 类说明
 */
public class ResultBean {

    /**
     * 构造函数：创建实体类时status默认值为：success
     */
    public ResultBean() {
        this.status = Constant.STATUS_SUCCESS;
        this.errorCode = "";
        this.message = "";
    }

    ;

    /**
     * 返回结果状态
     */
    private String status;

    /**
     * 返回结果
     */
    private Object content;

    /**
     * 提示信息集合
     */
    private Object message;

    /**
     * 错误码
     */
    private String errorCode;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public static ResultBean success() {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_SUCCESS);
        bean.setMessage(Constant.MESSAGE_SUCCESS);
        return bean;
    }

    public static ResultBean success(String message) {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_SUCCESS);
        bean.setMessage(StringUtils.isEmpty(message) ? Constant.MESSAGE_SUCCESS : message);
        return bean;
    }

    public static ResultBean success(Object content) {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_SUCCESS);
        bean.setMessage(Constant.MESSAGE_SUCCESS);
        bean.setContent(content);
        return bean;
    }

    public static ResultBean success(Object content, String message) {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_SUCCESS);
        bean.setMessage(StringUtils.isEmpty(message) ? Constant.MESSAGE_SUCCESS : message);
        bean.setContent(content);
        return bean;
    }

    public static ResultBean fail(String message) {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_FAIL);
        bean.setMessage(StringUtils.isEmpty(message) ? Constant.MESSAGE_FAIL : message);
        return bean;
    }

    public static ResultBean fail(String message, String errorCode) {
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_FAIL);
        bean.setErrorCode(errorCode);
        bean.setMessage(StringUtils.isEmpty(message) ? Constant.MESSAGE_FAIL : message);
        return bean;
    }

    public static ResultBean fail(List<FieldError> errors) {
        List<String> errorList = new ArrayList<String>();
        ResultBean bean = new ResultBean();
        bean.setStatus(Constant.STATUS_FAIL);
        for (FieldError fieldError : errors) {
            errorList.add(fieldError.getDefaultMessage());
        }
        bean.setMessage(String.join(",", errorList));
        return bean;
    }
}
