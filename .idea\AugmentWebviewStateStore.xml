<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;51939813-92ca-45d5-aee5-a05d9f6da867&quot;,&quot;conversations&quot;:{&quot;51939813-92ca-45d5-aee5-a05d9f6da867&quot;:{&quot;id&quot;:&quot;51939813-92ca-45d5-aee5-a05d9f6da867&quot;,&quot;createdAtIso&quot;:&quot;2025-08-02T13:29:14.798Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-02T13:29:14.798Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;9e9d34a5-4d0f-43dc-82dd-e13111cde1be&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>