package com.bello.lineage.controller;

import com.bello.lineage.common.bean.ResultBean;
import com.bello.lineage.common.constants.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 血缘分析
 *
 * <AUTHOR>
 * @date 2025/7/8
 * @see
 */
@Slf4j
@RestController
@RequestMapping(Constant.API_PREFIX + "/lineage")
public class LineageController {

    @GetMapping("/description")
    public ResultBean description() {
        log.info("血缘关系");
        return ResultBean.success("血缘关系");
    }
}
